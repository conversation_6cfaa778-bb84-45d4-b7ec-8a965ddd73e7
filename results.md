# Custom Form "Send to Email" Checkbox Implementation

## Summary

Successfully implemented a new checkbox field "Send the complete form to email" in the custom form editor modal. The checkbox is positioned directly below the existing "Published" checkbox as requested.

## Implementation Details

### Frontend Changes

1. **Template Update** (`frontend/events/dashboard/custom-forms-builder/custom-form-editor/custom-form-editor.template.html`)
   - Added new checkbox field with label "Send the complete form to email"
   - Positioned below the "Published" checkbox
   - Uses `ng-model="$ctrl.form.send_to_email"` for data binding

2. **Component Controller** (`frontend/events/dashboard/custom-forms-builder/custom-form-editor/custom-form-editor.component.js`)
   - Updated data binding to include `send_to_email` field in form data handling
   - Modified `_.pick()` calls to include the new field in both create and update modes

### Backend Changes

1. **Validation Schema** (`api/validation-schemas/custom-form.js`)
   - Added `send_to_email: Joi.boolean().allow(null).label('Send the complete form to email flag')` to validation

2. **Form Editing Service** (`api/services/event/custom-form/_FormEditingService.js`)
   - Updated `#prepareDataForCreation()` to handle the new field with default value `false`
   - Updated `#prepareDataForUpdating()` to handle the new field
   - Modified database query in `#getEventCustomFormRow()` to select the new field
   - Updated `groupBy` clause to include the new field

3. **Database Migration** (`db/migrations/main/20250820120000_SW-4600_add_send_to_email_to_custom_form_event.js`)
   - Added `send_to_email BOOLEAN DEFAULT FALSE` column to `custom_form_event` table
   - Includes rollback functionality to remove the column

## Database Schema Changes

The migration adds a new column to the `custom_form_event` table:

```sql
ALTER TABLE public.custom_form_event
    ADD COLUMN IF NOT EXISTS send_to_email BOOLEAN DEFAULT FALSE;
```

## Data Flow

1. **Frontend to Backend**: The checkbox value is sent as a boolean in the form data
2. **Backend Processing**: The value is validated and stored in the database
3. **Backend to Frontend**: The value is retrieved and sent back to populate the checkbox

## Testing Recommendations

1. **Create New Form**: Test creating a new custom form with the checkbox checked and unchecked
2. **Edit Existing Form**: Test editing an existing form and toggling the checkbox
3. **Data Persistence**: Verify the checkbox state is saved and restored correctly
4. **Migration**: Run the database migration to add the new column

## Files Modified

- `frontend/events/dashboard/custom-forms-builder/custom-form-editor/custom-form-editor.template.html`
- `frontend/events/dashboard/custom-forms-builder/custom-form-editor/custom-form-editor.component.js`
- `api/validation-schemas/custom-form.js`
- `api/services/event/custom-form/_FormEditingService.js`

## Files Created

- `db/migrations/main/20250820120000_SW-4600_add_send_to_email_to_custom_form_event.js`

## Next Steps

1. Run the database migration: `npm run migrate-main`
2. Test the implementation in the development environment
3. Verify the checkbox appears and functions correctly in both create and edit modes
4. Implement the actual email sending functionality (if required) based on this checkbox value

## Notes

- The implementation follows the existing patterns in the codebase
- No unit tests were modified as per the constraints
- The checkbox follows the same styling and structure as the existing "Published" checkbox
- The field is optional and defaults to `false` for backward compatibility
