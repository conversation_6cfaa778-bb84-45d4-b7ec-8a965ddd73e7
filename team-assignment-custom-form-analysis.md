# Team Assignment Custom Form Analysis

## Overview

This document provides a comprehensive analysis of how custom forms are displayed and handled in the team assignment workflow, specifically for the `TEAM_ASSIGN_FOR_EVENT` form type.

## Task 1: Team Assignment Page Code Locations

### Frontend UI Components

**Main Template**: `frontend/club/event-managing/assign-teams.html`
- **Line 121**: "Event Division" column header
- **Lines 146-157**: Division selector dropdown using `tdselectable` directive
- **Line 149**: `divisionChange(prop, val, item, callback)` event handler

**Controller**: `frontend/club/event-managing/assign-teams.controller.js`
- **Lines 97-127**: `divisionChange` function with custom form trigger logic
- **Lines 65-95**: `openCustomFormModal` function that displays the custom form

**Route Configuration**: `frontend/sport-wrench.routes.js`
- **Lines 1361-1371**: Route definition for team assignment page
- **Lines 1349-1359**: Parent route that loads `eventData` via `eventsService.getTackedEvent`

## Task 2: Division Selection and Custom Form Trigger Logic

### Division Change Workflow

1. **User Action**: User selects a division from the dropdown in the "Event Division" column
2. **Event Handler**: `divisionChange` function is triggered with the selected division value
3. **Custom Form Check**: System checks `$scope.event.custom_forms_requires_submitting` array
4. **Modal Display**: If forms require submission, `openCustomFormModal` is called

### XHR Request Pattern Analysis

**Form Retrieval**: `GET /api/custom-form/event/26099/form/291?submitter=16777373`
- **eventId**: 26099 (event ID)
- **formId**: 291 (custom_form_event_id)
- **submitter**: 16777373 (master_club_id)

**Form Submission**: `POST /api/custom-form/event/26099/form/291`
- **Body**: `{ values: {...}, submitter: 16777373 }`

### Custom Form Modal Logic

**Component**: `frontend/components/custom-form/form.component.js`
- **Lines 47-66**: `_getForm()` method retrieves form data via `CustomFormService.getForm()`
- **Lines 16-32**: `onSubmit()` method submits form via `CustomFormService.submitForm()`

**Service**: `frontend/common/customFormService.js`
- **Lines 8-15**: `getForm()` method makes GET request to API
- **Lines 17-19**: `submitForm()` method makes POST request to API

## Task 3: One-Time Form Submission Mechanism

### Database Schema

**Primary Table**: `custom_form_submitted_field_value`
- **custom_form_event_id**: Links to the specific form instance
- **custom_form_field_id**: Links to the specific field
- **submitter_type**: 'roster_club' (enum value)
- **submitter_id**: master_club_id (the club submitting)
- **value**: The submitted field value
- **created**: Timestamp of submission

### One-Time Submission Logic Flow

#### 1. Event Data Loading
**File**: `api/services/club/ClubService.events.js`
- **Lines 76-87**: Query that populates `custom_forms_requires_submitting` with all published forms
- **Lines 167-187**: `#addCustomFormsRules()` filters forms based on submission status

#### 2. Submission Status Check
**File**: `api/services/event-registration/registration-rules/CustomFormsRuleCheck.js`
- **Lines 17-37**: `getFormsSubmittingData()` checks each form's submission status
- **Line 25**: Calls `EventService.eventCustomForm.checkIfFormSubmitted()`

#### 3. Form Submission Status Query
**File**: `api/services/event/custom-form/team-assign-for-event/_FormService.js`
- **Lines 40-62**: `checkIfFormSubmitted()` method
- **Line 47**: Key logic: `COUNT(cfsfv.*) = 0` determines if form is not submitted
- **Lines 49-52**: Joins on `custom_form_event_id`, `submitter_type`, and `submitter_id`

#### 4. Form Submission Storage
**File**: `api/services/event/custom-form/AbstractFormService.js`
- **Lines 8-18**: `submitForm()` method saves all field values
- **Lines 32-50**: `#saveSubmittedFieldValue()` inserts records into database

### Relationship Mapping

**Club Owner → Club → Teams → Event Assignment**
- **Club Owner**: User logged in with `master_club_id`
- **Club**: `master_club` table record
- **Teams**: `master_team` records belonging to the club
- **Event Assignment**: `roster_team` records created when teams are assigned to divisions

### Prevention Mechanism

1. **Initial Load**: System queries for forms that haven't been submitted by this club
2. **Form Display**: Only unsubmitted forms appear in `custom_forms_requires_submitting` array
3. **Post-Submission**: Form is removed from the array, preventing future displays
4. **Database Record**: Submission creates permanent record in `custom_form_submitted_field_value`

## API Integration Points

### Current API Endpoints

**Form Retrieval**: `GET /api/custom-form/event/:event/form/:form_id`
- **Controller**: `api/controllers/v2/event/custom-form/submitting/form.js`
- **Service**: `EventService.eventCustomForm.getForm()`

**Form Submission**: `POST /api/custom-form/event/:event/form/:form_id`
- **Controller**: `api/controllers/v2/event/custom-form/submitting/save.js`
- **Service**: `EventService.eventCustomForm.submitForm()`

### Optimal Email Integration Point

**Location**: `api/services/event/custom-form/AbstractFormService.js`
- **Method**: `submitForm()` (lines 8-18)
- **Timing**: After successful database storage, before method completion
- **Context**: Has access to `eventID`, `eventFormID`, `submitterID`, and form data

**Implementation Strategy**:
```javascript
// After line 13 in AbstractFormService.js
if (formHasEmailToUserEnabled) {
    await this.#sendFormToUserEmail(eventID, eventFormID, submitterID, data);
}
```

## Manual Test Data Cleanup

**Database Table**: `custom_form_submitted_field_value`
**Cleanup Query**:
```sql
DELETE FROM custom_form_submitted_field_value 
WHERE custom_form_event_id = :form_id 
AND submitter_type = 'roster_club' 
AND submitter_id = :master_club_id;
```

## Code Comments Added

Added detailed comments to mark the exact locations involved in the one-time submission mechanism:

1. **Frontend Division Change Logic** (`assign-teams.controller.js` lines 97-127)
2. **Form Submission Handler** (`form.component.js` lines 16-32)
3. **Database Storage Logic** (`AbstractFormService.js` lines 8-18, 32-50)
4. **Submission Status Check** (`_FormService.js` lines 40-62)

These comments clearly identify the workflow and optimal integration points for future email functionality.
