
const {
    FORM_TYPE: { TEAM_ASSIGN_FOR_EVENT },
    SUBMITTER_TYPE: { ROSTER_CLUB }
} = require('../../../../constants/event-custom-form');
const AbstractFormService = require('../AbstractFormService');

class SoCalCupFormService extends AbstractFormService {
    constructor(props) {
        super(props);
    }

    FORM_TYPE = TEAM_ASSIGN_FOR_EVENT;
    SUBMITTER_TYPE = ROSTER_CLUB;

    async getDefaultValues (submitterID) {
        if(!submitterID) {
            return {};
        }

        const query = `
            SELECT club_name,
                   FORMAT('%s %s', mc.director_first, mc.director_last) director_name,
                   mc.administrative_email                              mailing_address,
                   mc.director_email                                    club_email,
                   mc.city                                              club_city,
                   mc.director_phone                                    club_phone
            FROM master_club mc
            WHERE mc.master_club_id = $1`;

        const { rows: [club] } = await Db.query(query, [submitterID]);

        if (_.isEmpty(club)) {
            throw { validation: 'Club not found' };
        }

        return club;
    }

    async checkIfFormSubmitted (eventFormID, submitterID)  {
        // ONE-TIME SUBMISSION CHECK: Query to determine if a form has already been submitted
        // This is the core logic that prevents duplicate form displays
        // Returns isNotSubmitted: true if no records found in custom_form_submitted_field_value table
        const query = knex('custom_form_event AS cfe')
            .select({
                formName: 'cfe.name',
                isNotSubmitted: knex.raw('COUNT(cfsfv.*) = 0') // Key logic: COUNT = 0 means not submitted
            })
            .leftJoin('custom_form_submitted_field_value AS cfsfv', join => {
                join.on('cfe.custom_form_event_id', 'cfsfv.custom_form_event_id')
                    .andOn(knex.raw(`cfsfv.submitter_type = ?`, [this.SUBMITTER_TYPE])) // 'roster_club'
                    .andOn(knex.raw(`cfsfv.submitter_id = ?`, [submitterID])) // master_club_id
            })
            .leftJoin('master_club AS mc', 'mc.master_club_id', 'cfsfv.submitter_id')
            .where('cfe.custom_form_event_id', eventFormID)
            .where('cfe.type', this.FORM_TYPE)
            .groupBy('cfe.name');

        const { rows: [submittingData] } = await Db.query(query);

        return submittingData;
    }

    getFormResultsSQL (eventID, eventFormID) {
        return knex.raw(`
           SELECT (json_agg(json_build_object(
                                     'value', (CASE
                                                   WHEN cfet.type = 'signature' AND cfsfv.value IS NOT NULL THEN 'Signed'
                                                   WHEN cfet.type = 'signature_checkbox' AND cfsfv.value::BOOLEAN IS TRUE
                                                       THEN 'Signed'
                                                   WHEN cfet.type = 'date' AND cfsfv.value IS NOT NULL
                                                       THEN TO_CHAR(cfsfv.value::timestamp, 'MM/DD/YY')
                                                   WHEN cfet.type = 'checkbox' AND cfsfv.value::BOOLEAN IS NOT TRUE THEN ''
                                                   ELSE cfsfv.value
                                               END),
                                     'label', FORMAT('%s (Sort ID: %s)', cff.label, cff.sort_order),
                                     'type', cfet.type,
                                     'options', (CASE WHEN cfet.type IN ('select', 'multiselect') THEN cff.options END)
                             ) ORDER BY cff.section, cff.sort_order))      "fields",
                   FORMAT('%s %s', mc.director_first, mc.director_last)    "Director Name",
                   TO_CHAR(MIN(cfsfv.created), 'Mon DD, YYYY, HH12:MI AM') "Submitted At (UTC)"
           FROM custom_form_submitted_field_value cfsfv
                     JOIN custom_form_field cff ON cfsfv.custom_form_field_id = cff.custom_form_field_id
                     JOIN custom_form_event cfe ON cfe.custom_form_event_id = cfsfv.custom_form_event_id
                     JOIN custom_form_field_type cfet ON cfet.custom_form_field_type_id = cff.custom_form_field_type_id
                     JOIN master_club mc
                          ON mc.master_club_id = cfsfv.submitter_id
                              AND cfsfv.submitter_type = 'roster_club'::custom_form_submitter_type
           WHERE cfe.event_id = ?
              AND cfe.type = ?
              AND cfe.custom_form_event_id = ?
           GROUP BY cfe.custom_form_event_id, mc.master_club_id`, [eventID, this.FORM_TYPE, eventFormID]).toString();
    }
}

module.exports = new SoCalCupFormService();
