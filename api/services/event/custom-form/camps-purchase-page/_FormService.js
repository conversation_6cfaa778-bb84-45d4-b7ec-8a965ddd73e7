
const {
    FORM_TYPE: { CAMPS_PURCHASE_PAGE },
    SUBMITTER_TYPE: { PURCHASE }
} = require('../../../../constants/event-custom-form');
const AbstractFormService = require('../AbstractFormService');

class SoCalCupFormService extends AbstractFormService {
    constructor(props) {
        super(props);
    }

    // PURCHASE FORM SUBMISSION: Uses 'purchase' submitter_type instead of 'roster_club'
    // This allows multiple submissions per purchase (no one-time restriction like roster_club)
    FORM_TYPE = CAMPS_PURCHASE_PAGE;
    SUBMITTER_TYPE = PURCHASE;

    // FORM DATA RETRIEVAL: Query to retrieve submitted purchase form data from database
    // This joins with the 'purchase' table to get purchaser information (first, last name)
    // Unlike roster_club forms, this can have multiple submissions per purchase_id
    getFormResultsSQL (eventID, eventFormID) {
        return knex.raw(`
            SELECT (json_agg(json_build_object(
                                     'value', (CASE
                                                   WHEN cfet.type = 'signature' AND cfsfv.value IS NOT NULL THEN 'Signed'
                                                   WHEN cfet.type = 'signature_checkbox' AND cfsfv.value::BOOLEAN IS TRUE
                                                       THEN 'Signed'
                                                   WHEN cfet.type = 'date' AND cfsfv.value IS NOT NULL
                                                       THEN TO_CHAR(cfsfv.value::timestamp, 'MM/DD/YY')
                                                   WHEN cfet.type = 'checkbox' AND cfsfv.value::BOOLEAN IS NOT TRUE THEN ''
                                                   ELSE cfsfv.value
                        END),
                                     'label', FORMAT('%s (Sort ID: %s)', cff.label, cff.sort_order),
                                     'type', cfet.type,
                                     'options', (CASE
                                                     WHEN cfet.type IN ('select', 'multiselect') THEN cff.options
                        END)
                             ) ORDER BY cff.section, cff.sort_order)) "fields",
                   FORMAT('%s %s', p.first, p.last)                   "Purchaser Name"
            FROM custom_form_submitted_field_value cfsfv
                     JOIN custom_form_field cff ON cfsfv.custom_form_field_id = cff.custom_form_field_id
                     JOIN custom_form_event cfe ON cfe.custom_form_event_id = cfsfv.custom_form_event_id
                     JOIN custom_form_field_type cfet ON cfet.custom_form_field_type_id = cff.custom_form_field_type_id
                     JOIN purchase p
                          ON p.purchase_id = cfsfv.submitter_id
                              AND cfsfv.submitter_type = 'purchase'::custom_form_submitter_type
            WHERE cfe.event_id = ?
              AND cfe.type = ?
              AND cfe.custom_form_event_id = ?
            GROUP BY cfe.custom_form_event_id, p.purchase_id;`, [eventID, this.FORM_TYPE, eventFormID]).toString();
    }
}

module.exports = new SoCalCupFormService();
