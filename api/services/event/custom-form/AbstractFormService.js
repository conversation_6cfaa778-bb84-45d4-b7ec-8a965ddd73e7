

class AbstractFormService {

    FORM_TYPE = null;
    SUBMITTER_TYPE = null;

    async submitForm (eventID, eventFormID, data, fields, submitterID) {
        // FORM DATA STORAGE: Save each field value to custom_form_submitted_field_value table
        // This creates the one-time submission record that prevents duplicate form displays (for roster_club)
        // Purchase forms allow multiple submissions per submitter_id
        for(const {id: fieldID} of fields) {
            await this.#saveSubmittedFieldValue(eventFormID, fieldID, data[fieldID], submitterID);
        }

        // EMAIL INTEGRATION POINT: Optimal location for email sending after successful database storage
        // Implementation should follow the save→read→email pattern:
        // 1. Data is already saved to database above
        // 2. Read the saved data back using getFormResultsSQL() method
        // 3. Send email with the verified database data to ensure data integrity
        // Example: await this.#sendFormToUserEmail(eventID, eventFormID, submitterID);
    }

    async getDefaultValues () {
        return {};
    }

    async getFormResultsSQL () {
        throw new Error('Method should be implemented');
    }

    async checkIfFormSubmitted () {
        throw new Error('Method should be implemented');
    }

    async #saveSubmittedFieldValue (eventFormID, eventFormFieldID, value, submitterID) {
        // DATABASE STORAGE: Insert form field values into custom_form_submitted_field_value table
        // This table tracks: custom_form_event_id, custom_form_field_id, submitter_type, submitter_id, value
        // The combination of custom_form_event_id + submitter_type + submitter_id creates the one-time submission mechanism
        const query = knex('custom_form_submitted_field_value')
            .insert({
                custom_form_event_id: eventFormID,
                custom_form_field_id: eventFormFieldID,
                submitter_type: this.SUBMITTER_TYPE,
                submitter_id: submitterID,
                value: Array.isArray(value) ? JSON.stringify(value) : value
            });

        const { rowCount } = await Db.query(query);

        if(!rowCount) {
            throw new Error('Form not submitted');
        }
    }
}

module.exports = AbstractFormService;
