
class CustomFormsRuleCheck {
    async check(customFormsRequiresSubmitting, eventID, masterClubID) {
        const formsData = await this.getFormsSubmittingData(
            customFormsRequiresSubmitting,
            eventID,
            masterClubID
        );

        for (const {isNotSubmitted, formName} of formsData) {
            if (isNotSubmitted) {
                throw {validation: `${formName} not submitted by Club`};
            }
        }
    }

    async getFormsSubmittingData(customFormsRequiresSubmitting, eventID, masterClubID) {
        let result = [];

        if (!_.isEmpty(customFormsRequiresSubmitting) && _.isArray(customFormsRequiresSubmitting)) {
            for (const { type: firmType, custom_form_event_id: customFormEventID } of customFormsRequiresSubmitting) {
                const {
                    isNotSubmitted,
                    formName
                } = await EventService.eventCustomForm.checkIfFormSubmitted(firmType, customFormEventID, masterClubID);

                result.push({
                    isNotSubmitted,
                    formName,
                    firmType,
                    customFormEventID
                });
            }
        }
        console.log('getFormsSubmittingData', result);

        return result;
    }
}

module.exports = new CustomFormsRuleCheck();
