
const fs= require('fs');

//GET /api/custom-form/event/:event/form/:form_id/export
module.exports = {
    friendlyName: 'Custom Event Form Results Export',
    description: 'Returns XLSX file with specific Custom Event Form results',

    inputs: {
        event: {
            type: 'number',
            description: 'Event ID'
        },
        form_id: {
            type: 'number',
            description: 'Event Id'
        }
    },

    fn: async function (inputs, exits) {
        const { event: eventID, form_id: formID } = inputs;

        try {
            // FORM DATA RETRIEVAL: Export submitted form data to XLSX file
            // This calls getFormResults which uses the appropriate form service's getFormResultsSQL method
            // The query retrieves data from custom_form_submitted_field_value table with proper joins
            const path = await EventService.eventCustomForm.getFormResults(eventID, formID);

            this.res.download(path, (err) => {
                if (err) {
                    loggers.errors_log.error('XLSX download error', err);

                    return this.res.serverError('No file created ' + path);
                } else {
                    fs.promises.unlink(path).catch((err) => loggers.errors_log.error(err));
                }
            })
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}
