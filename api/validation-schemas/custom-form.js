const Joi = require('joi');
const { EVENT_CUSTOM_FORM_TYPE } = require('../lib/joi-constants');

module.exports = {
    customFormData: Joi.object().keys({
        header_text: Joi.string().required().label('Form Header Text'),
        name: Joi.string().required().label('Form Name'),
        type: Joi.string().required().valid(
            EVENT_CUSTOM_FORM_TYPE.TEAM_ASSIGN_FOR_EVENT,
            EVENT_CUSTOM_FORM_TYPE.CAMPS_PURCHASE_PAGE
        ).label('Custom Form Placement'),
        published: Joi.boolean().allow(null).label('Custom Form publishing flag'),
        send_to_email: Joi.boolean().allow(null).label('Send the complete form to email flag')
    }),
    customFormField: Joi.object().keys({
        type: Joi.string().required().label('Field Type ID'),
        label: Joi.string().required().label('Field Label'),
        options: Joi.alternatives().conditional(Joi.ref('type'), {
            is: Joi.string().valid('select', 'multiselect'),
            then: Joi.array().items({
                id: Joi.number().required().label('Options ID'),
                label: Joi.string().required().label('Options Label')
            }).min(1).required(),
            otherwise: Joi.any().forbidden()
        }).label('Select / Multi-select Options'),
        is_required: Joi.boolean().required().label('Is Field Required Flag'),
        sort_order: Joi.number().required().min(0).label('Sort Order Value'),
        variation: Joi.alternatives().conditional(Joi.ref('type'), {
            is: 'text',
            then: Joi.string().valid('email', 'phone', 'url', 'default').required().label('Text Field Variation'),
            otherwise: Joi.any().forbidden()
        }),
        section: Joi.number().required().min(1).max(10).label('Field Section Value'),
        help_text: Joi.string().optional().allow(null, '').label('Help Text'),
        label_hover_text: Joi.string().optional().allow(null, '').label('Label Hover Text'),
        settings: Joi.object().optional().allow(null).label('Settings Object')
    }),
    fieldsOrderData: Joi.array().items({
        field_id: Joi.number().required().label('Field ID'),
        sort_order:  Joi.number().required().min(0).label('Sort Order Value')
    })
        .min(2)
        .unique((a, b) => a.field_id === b.field_id)
        .unique((a, b) => a.sort_order === b.sort_order)
};
