/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Add Puerto Rico to the state table
        INSERT INTO state (state, name, country)
        VALUES ('PR', 'Puerto Rico', 'US')
        ON CONFLICT (state) DO NOTHING;
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        -- Remove Puerto Rico from the state table
        DELETE FROM state
        WHERE state = 'PR' AND country = 'US';
    `)
};
