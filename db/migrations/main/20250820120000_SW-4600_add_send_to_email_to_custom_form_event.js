/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Add send_to_email column to custom_form_event table
        ALTER TABLE public.custom_form_event
            ADD COLUMN IF NOT EXISTS send_to_email BOOLEAN DEFAULT FALSE;
        
        -- Set default value for existing forms
        UPDATE public.custom_form_event
        SET send_to_email = FALSE
        WHERE send_to_email IS NULL;
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        -- Remove send_to_email column from custom_form_event table
        ALTER TABLE public.custom_form_event
            DROP COLUMN IF EXISTS send_to_email;
    `)
};
