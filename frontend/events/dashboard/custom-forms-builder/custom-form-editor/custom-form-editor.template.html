<modal-wrapper>
    <div class="row">
        <div class="col-xs-12">
            <form role="form" class="form-horizontal row-space" name="$ctrl.customFormForm">
                <div ng-class="{ 'form-group validation-required': true, 'has-error': $ctrl.fieldHasError('name') }">
                    <label class="col-sm-4 control-label">Name</label>
                    <div class="col-sm-7">
                        <input ng-model="$ctrl.form.name"
                               name="name"
                               type="text"
                               class="form-control"
                               placeholder="Name"
                               required
                        />
                    </div>
                </div>
                <div ng-class="{ 'form-group validation-required': true, 'has-error': $ctrl.fieldHasError('header_text') }">
                    <label class="col-sm-4 control-label">Header Text</label>
                    <div class="col-sm-7">
                        <input ng-model="$ctrl.form.header_text"
                               name="header_text"
                               type="text"
                               class="form-control"
                               placeholder="Header Text"
                               required
                        />
                    </div>
                </div>
                <div ng-class="{ 'form-group validation-required': true, 'has-error': $ctrl.fieldHasError('type') }">
                    <label class="col-sm-4 control-label">Form Placement</label>
                    <div class="col-sm-7">
                        <select
                            ng-model="$ctrl.form.type"
                            name="type"
                            class="form-control"
                            ng-options="fp.type as fp.title for fp in $ctrl.formPlacements"
                            ng-disabled="!$ctrl.form.editable"
                            required>
                            <option value="" ng-if="!$ctrl.form.type">Select...</option>
                        </select>
                    </div>
                </div>
                <div ng-class="{ 'form-group': true }">
                    <label class="col-sm-4 control-label">Published</label>
                    <div class="col-sm-5">
                        <label class="form-check-label">
                            <input type="checkbox" value="true" name="published" ng-model="$ctrl.form.published">
                        </label>
                    </div>
                </div>
                <div ng-class="{ 'form-group': true }">
                    <label class="col-sm-4 control-label">Send the complete form to email</label>
                    <div class="col-sm-5">
                        <label class="form-check-label">
                            <input type="checkbox" value="true" name="send_to_email" ng-model="$ctrl.form.send_to_email">
                        </label>
                    </div>
                </div>
            </form>
        </div>
        <div class="col-xs-7 col-xs-offset-5">
            <button class="btn btn-default pull-right"
                    ng-click="$ctrl.close()"
                    ng-if="$ctrl.form.fields.length > 10 || !$ctrl.form.fields"
            >Close</button>
            <button class="btn btn-success pull-right"
                    ng-click="$ctrl.save()"
                    ng-disabled="$ctrl.customFormForm.$invalid || !$ctrl.formDataChanged()"
            >Save</button>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.form.fields"><hr/></div>
    <div class="row">
        <div class="col-xs-12">
            <custom-form-fields-editor
                fields="$ctrl.form.fields"
                ng-if="$ctrl.formId"
                form-id="$ctrl.formId"
                allow-editing="$ctrl.form.editable"
            ></custom-form-fields-editor>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <button type="button"
                    ng-if="$ctrl.form.fields"
                    class="btn btn-default pull-right"
                    ng-click="$ctrl.close()"
            >Close</button>
        </div>
    </div>
</modal-wrapper>
