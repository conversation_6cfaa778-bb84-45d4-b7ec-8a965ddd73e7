
class Controller {
    constructor (CUSTOM_FORM_TYPE, $scope, customFormBuilderService, $stateParams, toastr) {
        this.CUSTOM_FORM_TYPE = CUSTOM_FORM_TYPE;
        this.$scope = $scope;
        this.customFormBuilderService = customFormBuilderService;
        this.eventID = $stateParams.event;
        this.toastr = toastr;
    }

    $onInit () {
        if(this.mode === 'update') {
            this.$scope.modalTitle = `<h4>Custom Form Edit</h4>`;
            this.$scope.modalSkipFooter = true;

            this.form = _.pick(this.formData, ['type', 'header_text', 'name', 'published', 'send_to_email', 'fields', 'editable']);
        } else {
            this.$scope.modalTitle = `<h4>Custom Form Creation</h4>`
            this.form = {
                editable: true
            };
        }

        this.formPlacements = [
            {
                type: this.CUSTOM_FORM_TYPE.CAMPS_PURCHASE_PAGE,
                title: 'Camps purchase page'
            },
            {
                type: this.CUSTOM_FORM_TYPE.TEAM_ASSIGN_FOR_EVENT,
                title: 'Team assign for event'
            }
        ]

        this.initialFormData = angular.copy(_.omit(this.form, ['fields']));
    }

    async save () {
        if (this.customFormForm.$invalid) {
            this.customFormForm.$setSubmitted();
            return;
        }

        let dataForSaving = _.omit(this.form, ['fields', 'editable']);

        try {
            if (this.mode === 'update') {
                await this.customFormBuilderService.updateForm(this.eventID, this.formId, dataForSaving);
            } else {
                let form = await this.customFormBuilderService.createForm(this.eventID, dataForSaving);

                this.form = _.pick(form, ['type', 'header_text', 'name', 'published', 'send_to_email', 'fields']);
                this.form.editable = true;
                this.formId = form.custom_form_event_id;
                this.mode = 'update';
            }

            this.initialFormData = angular.copy(_.omit(this.form, ['fields']));

            this.toastr.success('Updated');
        } catch (err) {
            console.error(err);
        }
    }

    close () {
        this.form = {};
        this.onClose();
    }

    fieldHasError (fieldName) {
        return this.customFormForm.$submitted &&
            this.customFormForm[fieldName] &&
            this.customFormForm[fieldName].$invalid
    }

    formDataChanged () {
        return JSON.stringify(this.initialFormData) !== JSON.stringify(_.omit(this.form, ['fields']));
    }
}

Controller.$inject = ['CUSTOM_FORM_TYPE', '$scope', 'customFormBuilderService', '$stateParams', 'toastr'];

angular.module('SportWrench').component('customFormsEditor', {
    templateUrl: 'events/dashboard/custom-forms-builder/custom-form-editor/custom-form-editor.template.html',
    bindings: {
        formData: '<',
        mode: '<',
        onClose: '&',
        formId: '<'
    },
    controller: Controller
});
