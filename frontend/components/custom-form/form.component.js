
class Controller {
    constructor (CustomFormService, toastr) {
        this.CustomFormService = CustomFormService;
        this.toastr = toastr;
    }

    $onInit () {
        this.formDataLoading = false;
        this.form = {};
        this.values = {};

        this._getForm();
    }

    onSubmit () {
        const isInvalidForm = this.__validateFields(this.form.fields, this.values);

        if(isInvalidForm) {
            this.toastr.warning('Invalid Form Data');
            return;
        }

        // FORM SUBMISSION: Submit form data to API endpoint /api/custom-form/event/:event/form/:form_id
        // This triggers the backend submission logic and database storage
        this.CustomFormService.submitForm(this.eventId, this.customFormEventId, this.values, this.submitter)
            .then(() => {
                this.toastr.success('Submitted!');
                // OPTIMAL EMAIL INTEGRATION POINT: After successful submission, the backend could trigger email sending here
                this.onCancel({submitted: true});
            });
    }

    cancel () {
        this.customForm.$setPristine();
        this.values = Object.assign({});
        this.form = null;

        this.onCancel({submitted: false});
    }

    getFieldName (field) {
        return `${field.type}_${field.id}`;
    }

    fieldHasError (field) {
        return field.is_invalid;
    }

    async _getForm () {
        this.formDataLoading = true;
        try {
            const form = await this.CustomFormService.getForm(this.eventId, this.customFormEventId, this.submitter);

            form.fields = form.fields.map(f => {
                if(f.default_value) {
                    this.values[f.id] = f.default_value;
                }

                return Object.assign({name: this.getFieldName(f)}, f);
            })

            form.sections = _.groupBy(form.fields, 'section');

            this.form = form;
        } finally {
            this.formDataLoading = false;
        }
    }

    __validateFields (fields, values) {
        let hasInvalidFields = false;
        fields.forEach(f => {
            let emptyValue = !values[f.id];

            if(f.type === 'multiselect') {
                emptyValue = emptyValue || !values[f.id].length;
            }

            if(f.is_required && emptyValue || (this.customForm[f.name] && this.customForm[f.name].$invalid)) {
                f.is_invalid = true;

                hasInvalidFields = true;
            } else {
                f.is_invalid = false;
            }
        });

        return hasInvalidFields;
    }

    showSectionLine (index) {
        return index !== Object.keys(this.form.sections).length - 1;
    }
}

Controller.$inject = ['CustomFormService', 'toastr'];

angular.module('SportWrench').component('customForm', {
    templateUrl: 'components/custom-form/form.template.html',
    bindings: {
        type: '<',
        eventId: '<',
        customFormEventId: '<',
        onCancel: '&',
        submitter: '<'
    },
    controller: Controller
});
