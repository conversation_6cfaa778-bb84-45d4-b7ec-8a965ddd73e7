#!/bin/bash

# Wait for <PERSON><PERSON><PERSON> to be ready
sleep 10

# Create the email-service vhost (if not already created by environment variable)
rabbitmqctl add_vhost email-service || true

# Set permissions for the user on the vhost
rabbitmqctl set_permissions -p email-service rabbitmq ".*" ".*" ".*"

# Create the notification exchange
rabbitmqadmin -u rabbitmq -p change-in-production -V email-service declare exchange name=notification type=topic

echo "RabbitMQ setup completed!"
