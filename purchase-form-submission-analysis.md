# Purchase Form Submission Analysis

## Overview

This document analyzes the purchase form submission flow (`submitter_type = "purchase"`) and compares it with the roster_club flow. It also identifies form data retrieval mechanisms and optimal email integration points.

## Task 1: Purchase Form Submission Code Locations

### Backend Services

**Purchase Form Service**: `api/services/event/custom-form/camps-purchase-page/_FormService.js`
- **Lines 8-16**: PURCHASE FORM SUBMISSION configuration
- **SUBMITTER_TYPE**: `PURCHASE` (vs `ROSTER_CLUB` for team assignment forms)
- **FORM_TYPE**: `CAMPS_PURCHASE_PAGE`

**Key Differences from Roster_Club Forms**:
1. **No `checkIfFormSubmitted` method**: Purchase forms don't implement one-time submission restrictions
2. **Multiple submissions allowed**: Same purchase_id can submit forms multiple times
3. **Different submitter context**: Uses `purchase_id` instead of `master_club_id`

### API Endpoints

**Same endpoints used for both submitter types**:
- **Form Retrieval**: `GET /api/custom-form/event/:event/form/:form_id?submitter={purchase_id}`
- **Form Submission**: `POST /api/custom-form/event/:event/form/:form_id`
  - Body: `{ values: {...}, submitter: {purchase_id} }`

**Controllers**: Same as roster_club forms
- `api/controllers/v2/event/custom-form/submitting/form.js`
- `api/controllers/v2/event/custom-form/submitting/save.js`

### Submission Flow Differences

| Aspect | Roster_Club Forms | Purchase Forms |
|--------|------------------|----------------|
| **Submitter Type** | `'roster_club'` | `'purchase'` |
| **Submitter ID** | `master_club_id` | `purchase_id` |
| **Submission Limit** | One-time per club/event | Multiple allowed |
| **Default Values** | Club director info | None (empty object) |
| **Validation Check** | `checkIfFormSubmitted()` | Not implemented |
| **Frontend Trigger** | Team assignment workflow | Purchase page workflow |

## Task 2: Form Data Retrieval Logic

### Database Schema

**Primary Table**: `custom_form_submitted_field_value`
- **custom_form_event_id**: Form instance identifier
- **custom_form_field_id**: Specific field identifier  
- **submitter_type**: `'roster_club'` or `'purchase'`
- **submitter_id**: `master_club_id` or `purchase_id`
- **value**: Submitted field value
- **created**: Submission timestamp

### Form Data Retrieval Methods

#### Roster_Club Form Data Retrieval
**File**: `api/services/event/custom-form/team-assign-for-event/_FormService.js`
- **Lines 64-96**: FORM DATA RETRIEVAL query
- **Joins**: `master_club` table for director information
- **Output**: Director name, submission timestamp, formatted field values

#### Purchase Form Data Retrieval  
**File**: `api/services/event/custom-form/camps-purchase-page/_FormService.js`
- **Lines 18-51**: FORM DATA RETRIEVAL query
- **Joins**: `purchase` table for purchaser information
- **Output**: Purchaser name (first, last), formatted field values

### Export Functionality

**Export Endpoint**: `GET /api/custom-form/event/:event/form/:form_id/export`
- **Controller**: `api/controllers/v2/event/custom-form/submitting/export-results.js`
- **Lines 20-41**: FORM DATA RETRIEVAL export logic
- **Service**: `EventService.eventCustomForm.getFormResults()`
- **Output**: XLSX file with submitted form data

**Main Export Service**: `api/services/event/_EventCustomFormService.js`
- **Lines 57-67**: FORM DATA RETRIEVAL main method
- **Process**: Determines form type → calls appropriate service → exports to XLSX

### Admin/Reporting Interfaces

**Current Implementation**: 
- Export functionality provides XLSX download of submitted form data
- No web-based admin interface found for viewing submissions
- Data retrieval is primarily for export purposes

## Task 3: Optimal Email Integration Points

### Primary Integration Point

**Location**: `api/services/event/custom-form/AbstractFormService.js`
- **Method**: `submitForm()` (lines 8-22)
- **Timing**: After successful database storage
- **Context**: Access to `eventID`, `eventFormID`, `submitterID`

### Recommended Email Implementation Pattern

**Save → Read → Email Pattern**:

```javascript
async submitForm (eventID, eventFormID, data, fields, submitterID) {
    // 1. SAVE: Store form data to database
    for(const {id: fieldID} of fields) {
        await this.#saveSubmittedFieldValue(eventFormID, fieldID, data[fieldID], submitterID);
    }
    
    // 2. READ: Retrieve saved data from database for email
    // 3. EMAIL: Send email with verified database data
    await this.#sendFormToUserEmail(eventID, eventFormID, submitterID);
}

async #sendFormToUserEmail(eventID, eventFormID, submitterID) {
    // Check if email_to_user is enabled for this form
    const formSettings = await this.#getFormSettings(eventFormID);
    if (!formSettings.email_to_user) return;
    
    // Read saved data from database using existing query
    const formDataQuery = this.getFormResultsSQL(eventID, eventFormID);
    const savedFormData = await Db.query(formDataQuery);
    
    // Get user email based on submitter type
    const userEmail = await this.#getUserEmail(submitterID);
    
    // Send email with complete form data
    await EmailService.sendFormSubmissionEmail(userEmail, savedFormData);
}
```

### Email Integration Benefits

1. **Data Integrity**: Email contains exactly what was stored in database
2. **Consistency**: Same data retrieval logic used for exports and emails
3. **Reliability**: Verified database storage before email sending
4. **Maintainability**: Reuses existing form data formatting logic

### Submitter-Specific Email Retrieval

**Roster_Club Forms**:
- **Email Source**: `master_club.director_email` or `master_club.administrative_email`
- **Query**: Join with `master_club` table using `submitter_id`

**Purchase Forms**:
- **Email Source**: `purchase.email` (likely field in purchase table)
- **Query**: Join with `purchase` table using `submitter_id`

## Key Differences Summary

### Submission Behavior
- **Roster_Club**: One-time submission per club/event with validation checks
- **Purchase**: Multiple submissions allowed per purchase without restrictions

### Data Context
- **Roster_Club**: Club director information, team assignment context
- **Purchase**: Purchaser information, camps/purchase context

### Form Data Retrieval
- **Both types**: Use same database table structure
- **Different joins**: Roster_club joins with `master_club`, purchase joins with `purchase`
- **Same export mechanism**: Both use `getFormResultsSQL()` pattern

### Email Integration
- **Same integration point**: `AbstractFormService.submitForm()`
- **Different email sources**: Club director email vs purchaser email
- **Same data retrieval pattern**: Use existing `getFormResultsSQL()` methods

## Code Comments Added

Added comments with prefixes as requested:
- **PURCHASE FORM SUBMISSION**: Purchase form service configuration
- **FORM DATA RETRIEVAL**: Database queries and export functionality  
- **EMAIL INTEGRATION POINT**: Optimal locations for email sending logic

All comments clearly identify the locations and explain the differences between submitter types.
